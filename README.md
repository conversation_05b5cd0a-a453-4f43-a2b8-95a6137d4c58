# Marketplace Functions

Firebase Functions for the marketplace application with TON blockchain integration and Telegram authentication.

## Quick Start

1. **Setup Configuration**

   ```bash
   cd functions
   # Edit .env with your actual values
   npm run config:setup
   ```

2. **Build and Deploy**

   ```bash
   npm run build
   firebase deploy --only functions
   ```

3. **Deploy Rules**

   ```bash
   firebase deploy --only firestore:rules
   firebase deploy --only storage:rules
   ```

4. Export Indexes:
   ```bash
   firebase firestore:indexes > firestore.indexes.json && firebase deploy --only firestore:indexes
   ```

## Configuration

See [functions/CONFIG_SETUP.md](functions/CONFIG_SETUP.md) for detailed configuration instructions.

## FIREBASE COMMANDS

```
firebase use dev
firebase use prod
```
