# Firebase Cloud Functions v2 Migration Guide

## Overview

This guide explains how to migrate from Firebase Cloud Functions v1 to v2, specifically for the `cancelOrder` function and the configuration system.

## Changes Made

### 1. Updated cancelOrder Function

The `cancelOrder` function has been migrated from v1 to v2:

**Before (v1):**
```typescript
import * as functions from "firebase-functions";

export const cancelOrder = functions.https.onCall(async (data, context) => {
  // v1 implementation
});
```

**After (v2):**
```typescript
import { onCall, HttpsError } from "firebase-functions/v2/https";

export const cancelOrder = onCall(async (request) => {
  // v2 implementation
});
```

### 2. Updated Configuration System

The configuration system has been updated to use environment variables instead of `functions.config()`:

**Before (v1):**
```typescript
const config = functions.config();
```

**After (v2):**
```typescript
// Uses process.env directly
const botToken = process.env.TELEGRAM_BOT_TOKEN;
```

## Environment Variables Setup

For v2 functions, you need to set environment variables. You can do this in several ways:

### Option 1: Using Firebase CLI (Recommended)

Set environment variables using the Firebase CLI:

```bash
# Set individual variables
firebase functions:config:set telegram.bot_token="your-bot-token"
firebase functions:config:set ton.marketplace_wallet="your-wallet-address"

# Or use the existing setup script
npm run config:setup:dev  # for development
npm run config:setup:prod # for production
```

### Option 2: Using .env Files (Local Development)

For local development with the emulator, create a `.env` file in the functions directory:

```bash
# functions/.env
TELEGRAM_BOT_TOKEN=your-bot-token
TELEGRAM_LOCAL_BOT_TOKEN=your-local-bot-token
TELEGRAM_API_ID=your-api-id
TELEGRAM_API_HASH=your-api-hash
TON_MARKETPLACE_WALLET=your-wallet-address
TON_MARKETPLACE_WALLET_MNEMONIC=your-wallet-mnemonic
TON_RPC_URL_MAINNET=your-rpc-url
APP_ENVIRONMENT=development
```

### Option 3: Using Firebase Functions Params (Future Enhancement)

For better security and validation, consider migrating to Firebase Functions params:

```typescript
import { defineString, defineSecret } from "firebase-functions/params";

const botToken = defineSecret("TELEGRAM_BOT_TOKEN");
const walletAddress = defineString("TON_MARKETPLACE_WALLET");

export const cancelOrder = onCall(
  { secrets: [botToken] },
  async (request) => {
    const token = botToken.value();
    // function implementation
  }
);
```

## Required Environment Variables

The following environment variables are required:

- `TELEGRAM_BOT_TOKEN` - Telegram bot token
- `TELEGRAM_LOCAL_BOT_TOKEN` - Local development bot token
- `TELEGRAM_API_ID` - Telegram API ID
- `TELEGRAM_API_HASH` - Telegram API hash
- `TON_MARKETPLACE_WALLET` - Marketplace wallet address
- `TON_MARKETPLACE_WALLET_MNEMONIC` - Wallet mnemonic phrase
- `TON_RPC_URL_MAINNET` - TON RPC URL

## Deployment

1. **Set Environment Variables:**
   ```bash
   npm run config:setup:prod
   ```

2. **Deploy the Function:**
   ```bash
   firebase deploy --only functions:cancelOrder
   ```

3. **Verify Deployment:**
   ```bash
   firebase functions:log
   ```

## Testing

1. **Local Testing:**
   ```bash
   npm run serve
   ```

2. **Test the Function:**
   Use the Firebase Functions emulator UI or call the function from your client app.

## Troubleshooting

### Error: "functions.config() is no longer available"

This error occurs when v1 configuration methods are used in v2 functions. Ensure all functions use environment variables or params.

### Missing Environment Variables

If you get configuration errors:

1. Check that all required environment variables are set
2. Verify the `.env` file exists for local development
3. Use `firebase functions:config:get` to check deployed config

### Function Not Found

If the function doesn't appear after deployment:

1. Check the build output for errors
2. Verify the function is exported in `index.ts`
3. Check Firebase console for deployment status

## Benefits of v2 Migration

1. **Better Performance** - Runs on Cloud Run infrastructure
2. **Improved Concurrency** - Handle multiple requests simultaneously
3. **Better Monitoring** - Enhanced logging and metrics
4. **Modern Architecture** - Built on latest Google Cloud technologies

## Next Steps

1. Test the migrated `cancelOrder` function thoroughly
2. Consider migrating other functions to v2
3. Implement Firebase Functions params for better security
4. Update client-side code if needed (function signatures remain the same)
