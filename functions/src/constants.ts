// Order-related constants
// Both SELLER_LOCK_PERCENTAGE and BUYER_LOCK_PERCENTAGE are now configurable via app_config
export const DEFAULT_SELLER_LOCK_PERCENTAGE = 0.2; // 20% default if not configured
export const DEFAULT_BUYER_LOCK_PERCENTAGE = 1.0; // 100% default if not configured
export const FIXED_REJECTION_FEE_TON = 0.1; // Fixed 0.1 TON fee for seller rejecting order with no buyer

// Transaction monitoring constants
export const MIN_TRANSACTION_THRESHOLD_TON = 0.9; // Minimum transaction amount to process (> 0.9 TON)

// Default fee values in BPS (basis points) - used as fallbacks
export const DEFAULT_REJECT_ORDER_FEE_BPS = 1000; // 10% default rejection fee

// Fee calculation constants
export const BPS_DIVISOR = 10000; // 1 BPS = 0.01%, so divide by 10000 to get decimal
export const PENALTY_SPLIT_RATIO = 0.5; // 50% split for penalties between marketplace and counterparty

// Collection and user constants
export const MARKETPLACE_REVENUE_USER_ID = "marketplace_revenue";
export const APP_CONFIG_COLLECTION = "app_config";
export const APP_CONFIG_DOC_ID = "fees";
