import { onCall, HttpsError } from "firebase-functions/v2/https";
import * as admin from "firebase-admin";
import {
  requireAuthentication,
  validateOrderCreationParams,
  validateBuyerOwnership,
  validatePurchaseParams,
} from "../services/auth-middleware";
import { createBuyerOrder } from "../services/order-creation-service";
import { processBuyerPurchase } from "../services/purchase-flow-service";

export const createOrderAsBuyer = onCall(async (request) => {
  // Validate authentication and input
  const authRequest = requireAuthentication(request);
  const { buyerId, collectionId, amount, owned_gift_id } = request.data;

  validateOrderCreationParams(request.data, "buyer");
  validateBuyerOwnership(authRequest, buyerId);

  try {
    const db = admin.firestore();

    // Use order creation service
    const result = await createBuyerOrder(
      db,
      buyerId,
      collectionId,
      amount,
      owned_gift_id
    );

    return result;
  } catch (error) {
    console.error("Error creating order as buyer:", error);
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while creating order."
    );
  }
});

export const makePurchaseAsBuyer = onCall(async (request) => {
  // Validate authentication and input
  const authRequest = requireAuthentication(request);
  const { buyerId, orderId } = request.data;

  validatePurchaseParams(request.data, "buyer");
  validateBuyerOwnership(authRequest, buyerId);

  try {
    const db = admin.firestore();

    // Use purchase flow service
    const result = await processBuyerPurchase(db, buyerId, orderId);

    return result;
  } catch (error) {
    console.error("Error making purchase as buyer:", error);
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while making purchase."
    );
  }
});
