import { onCall, HttpsError } from "firebase-functions/v2/https";
import * as admin from "firebase-admin";
import {
  requireAuthentication,
  validateOrderCreationParams,
  validateSellerOwnership,
  validatePurchaseParams,
} from "../services/auth-middleware";
import { createSellerOrder } from "../services/order-creation-service";
import { processSellerPurchase } from "../services/purchase-flow-service";

export const createOrderAsSeller = onCall(async (request) => {
  // Validate authentication and input
  const authRequest = requireAuthentication(request);
  const { sellerId, collectionId, amount, owned_gift_id } = request.data;

  validateOrderCreationParams(request.data, "seller");
  validateSellerOwnership(authRequest, sellerId);

  try {
    const db = admin.firestore();

    // Use order creation service
    const result = await createSellerOrder(
      db,
      sellerId,
      collectionId,
      amount,
      owned_gift_id
    );

    return result;
  } catch (error) {
    console.error("Error creating order as seller:", error);
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while creating order."
    );
  }
});

export const makePurchaseAsSeller = onCall(async (request) => {
  // Validate authentication and input
  const authRequest = requireAuthentication(request);
  const { sellerId, orderId } = request.data;

  validatePurchaseParams(request.data, "seller");
  validateSellerOwnership(authRequest, sellerId);

  try {
    const db = admin.firestore();

    // Use purchase flow service
    const result = await processSellerPurchase(db, sellerId, orderId);

    return result;
  } catch (error) {
    console.error("Error making purchase as seller:", error);
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while making purchase."
    );
  }
});
