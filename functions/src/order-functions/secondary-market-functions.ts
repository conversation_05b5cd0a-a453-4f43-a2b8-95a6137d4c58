import * as admin from "firebase-admin";
import { on<PERSON>all, HttpsError } from "firebase-functions/v2/https";
import {
  getAppConfig,
  applyPurchaseFeeWithReferral,
} from "../services/fee-service";
import { OrderEntity, OrderStatus, UserEntity } from "../types";
import {
  updateUserBalance,
  validateSufficientFunds,
} from "../services/balance-service";
import { DEFAULT_BUYER_LOCK_PERCENTAGE } from "../constants";
import { safeMultiply, safeSubtract } from "../utils";

export const setSecondaryMarketPrice = onCall(
  { cors: true },
  async (request) => {
    const { orderId, secondaryMarketPrice } = request.data;
    const userId = request.auth?.uid;

    if (!userId) {
      throw new HttpsError("unauthenticated", "User must be authenticated.");
    }

    if (!orderId || typeof orderId !== "string") {
      throw new HttpsError("invalid-argument", "Valid order ID is required.");
    }

    if (
      secondaryMarketPrice === null ||
      secondaryMarketPrice === undefined ||
      typeof secondaryMarketPrice !== "number" ||
      secondaryMarketPrice <= 0
    ) {
      throw new HttpsError(
        "invalid-argument",
        "Valid secondary market price is required."
      );
    }

    try {
      const db = admin.firestore();

      // Get order
      const orderDoc = await db.collection("orders").doc(orderId).get();
      if (!orderDoc.exists) {
        throw new HttpsError("not-found", "Order not found.");
      }

      const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

      // Validate order status
      if (order.status !== OrderStatus.PAID) {
        throw new HttpsError(
          "failed-precondition",
          "Only orders with PAID status can be listed on secondary market."
        );
      }

      // Validate user is the buyer
      if (order.buyerId !== userId) {
        throw new HttpsError(
          "permission-denied",
          "Only the current buyer can set secondary market price."
        );
      }

      // Validate order has both buyer and seller
      if (!order.buyerId || !order.sellerId) {
        throw new HttpsError(
          "failed-precondition",
          "Order must have both buyer and seller to be listed on secondary market."
        );
      }

      // Get app config for minimum price validation
      const config = await getAppConfig();
      const minSecondaryMarketPrice = config?.min_secondary_market_price ?? 1;

      if (secondaryMarketPrice < minSecondaryMarketPrice) {
        throw new HttpsError(
          "invalid-argument",
          `Secondary market price must be at least ${minSecondaryMarketPrice} TON.`
        );
      }

      // Update order with secondary market price
      await db.collection("orders").doc(orderId).update({
        secondaryMarketPrice,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      return {
        success: true,
        message: `Secondary market price set to ${secondaryMarketPrice} TON.`,
        orderId,
        secondaryMarketPrice,
      };
    } catch (error) {
      console.error("Error setting secondary market price:", error);
      throw new HttpsError(
        "internal",
        (error as any).message ??
          "Server error while setting secondary market price."
      );
    }
  }
);

export const makeSecondaryMarketPurchase = onCall(
  { cors: true },
  async (request) => {
    const { orderId } = request.data;
    const newBuyerId = request.auth?.uid;

    if (!newBuyerId) {
      throw new HttpsError("unauthenticated", "User must be authenticated.");
    }

    if (!orderId || typeof orderId !== "string") {
      throw new HttpsError("invalid-argument", "Valid order ID is required.");
    }

    try {
      const db = admin.firestore();

      // Get order
      const orderDoc = await db.collection("orders").doc(orderId).get();
      if (!orderDoc.exists) {
        throw new HttpsError("not-found", "Order not found.");
      }

      const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

      // Validate order has secondary market price set
      if (!order.secondaryMarketPrice || order.secondaryMarketPrice <= 0) {
        throw new HttpsError(
          "failed-precondition",
          "Order is not available on secondary market."
        );
      }

      // Validate order status
      if (order.status !== OrderStatus.PAID) {
        throw new HttpsError(
          "failed-precondition",
          "Only orders with PAID status can be purchased on secondary market."
        );
      }

      // Validate new buyer is not the seller or current buyer
      if (newBuyerId === order.sellerId) {
        throw new HttpsError(
          "permission-denied",
          "Seller cannot purchase their own order on secondary market."
        );
      }

      if (newBuyerId === order.buyerId) {
        throw new HttpsError(
          "permission-denied",
          "Current buyer cannot purchase their own order on secondary market."
        );
      }

      // Validate order has both buyer and seller
      if (!order.buyerId || !order.sellerId) {
        throw new HttpsError(
          "failed-precondition",
          "Order must have both buyer and seller for secondary market purchase."
        );
      }

      // Get new buyer data for referral
      const newBuyerDoc = await db.collection("users").doc(newBuyerId).get();
      if (!newBuyerDoc.exists) {
        throw new HttpsError("not-found", "New buyer not found.");
      }

      const newBuyerData = newBuyerDoc.data() as UserEntity;
      const referralId = newBuyerData?.referral_id;

      // Get app config for lock percentages
      const config = await getAppConfig();
      const buyerLockPercentage =
        config?.buyer_lock_percentage ?? DEFAULT_BUYER_LOCK_PERCENTAGE;

      // Calculate amounts
      const secondaryMarketPrice = order.secondaryMarketPrice;

      // Calculate old buyer's locked amount (from original order) - this will be transferred to new buyer
      const oldBuyerLockedAmount = safeMultiply(
        order.amount,
        buyerLockPercentage
      );

      // Apply purchase fee with referral
      const feeResult = await applyPurchaseFeeWithReferral(
        newBuyerId,
        secondaryMarketPrice,
        referralId
      );

      const netAmountToOldBuyer = safeSubtract(
        secondaryMarketPrice,
        feeResult.totalFee
      );

      // Validate new buyer has sufficient funds for secondary market purchase
      await validateSufficientFunds(
        newBuyerId,
        secondaryMarketPrice,
        "secondary market purchase"
      );

      // Step 1: New buyer pays secondary market price and gets locked amount from old buyer
      // New buyer: sum = sum - secondaryMarketPrice + oldBuyerLockedAmount, locked = locked + oldBuyerLockedAmount
      await updateUserBalance(
        newBuyerId,
        -secondaryMarketPrice + oldBuyerLockedAmount,
        oldBuyerLockedAmount
      );

      // Step 2: Old buyer receives net amount and loses their locked amount
      // Old buyer: sum = sum + netAmountToOldBuyer - oldBuyerLockedAmount, locked = locked - oldBuyerLockedAmount
      await updateUserBalance(
        order.buyerId,
        netAmountToOldBuyer - oldBuyerLockedAmount,
        -oldBuyerLockedAmount
      );

      // Update order with new buyer and clear secondary market price
      await db.collection("orders").doc(orderId).update({
        buyerId: newBuyerId,
        secondaryMarketPrice: null,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      return {
        success: true,
        message: `Secondary market purchase successful! Paid ${secondaryMarketPrice} TON (${feeResult.totalFee} TON fee). ${oldBuyerLockedAmount} TON locked.`,
        orderId,
        secondaryMarketPrice,
        newBuyerId,
        oldBuyerId: order.buyerId,
        netAmountToOldBuyer,
        feeAmount: feeResult.totalFee,
        lockedAmount: oldBuyerLockedAmount,
      };
    } catch (error) {
      console.error("Error making secondary market purchase:", error);
      throw new HttpsError(
        "internal",
        (error as any).message ??
          "Server error while making secondary market purchase."
      );
    }
  }
);
