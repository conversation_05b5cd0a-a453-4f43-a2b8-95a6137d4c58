import * as admin from "firebase-admin";
import { onCall, HttpsError } from "firebase-functions/v2/https";
import { unlockFunds } from "../services/balance-service";
import {
  DEFAULT_BUYER_LOCK_PERCENTAGE,
  DEFAULT_SELLER_LOCK_PERCENTAGE,
} from "../constants";
import { getAppConfig } from "../services/fee-service";
import { OrderEntity, OrderStatus } from "../types";

export const cancelOrder = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication required.");
  }

  const { orderId, userId } = request.data;

  if (!orderId || !userId) {
    throw new HttpsError(
      "invalid-argument",
      "orderId and userId are required."
    );
  }

  if (request.auth.uid !== userId) {
    throw new HttpsError(
      "permission-denied",
      "You can only cancel your own orders."
    );
  }

  try {
    const db = admin.firestore();

    // Get order
    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Check if user is authorized to cancel this order
    if (order.buyerId !== userId && order.sellerId !== userId) {
      throw new HttpsError(
        "permission-denied",
        "You can only cancel orders where you are the buyer or seller."
      );
    }

    // Check if order can be cancelled
    if (order.status === OrderStatus.FULFILLED) {
      throw new HttpsError(
        "failed-precondition",
        "Cannot cancel a fulfilled order."
      );
    }

    if (order.status === OrderStatus.CANCELLED) {
      throw new HttpsError(
        "failed-precondition",
        "Order is already cancelled."
      );
    }

    // Get app config for lock percentages
    const config = await getAppConfig();
    const buyerLockPercentage =
      config?.buyer_lock_percentage ?? DEFAULT_BUYER_LOCK_PERCENTAGE;
    const sellerLockPercentage =
      config?.seller_lock_percentage ?? DEFAULT_SELLER_LOCK_PERCENTAGE;

    // Calculate locked amounts
    const buyerLockedAmount = order.amount * buyerLockPercentage;
    const sellerLockedAmount = order.amount * sellerLockPercentage;

    // Unlock funds based on order status and participants
    const unlockPromises = [];

    if (order.buyerId) {
      unlockPromises.push(unlockFunds(order.buyerId, buyerLockedAmount));
    }

    if (order.sellerId) {
      unlockPromises.push(unlockFunds(order.sellerId, sellerLockedAmount));
    }

    await Promise.all(unlockPromises);

    // Update order status to cancelled
    await db.collection("orders").doc(orderId).update({
      status: OrderStatus.CANCELLED,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message: "Order cancelled successfully. Locked funds have been released.",
      order: {
        id: order.id,
        number: order.number,
        status: OrderStatus.CANCELLED,
      },
    };
  } catch (error) {
    console.error("Error cancelling order:", error);
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while cancelling order."
    );
  }
});
