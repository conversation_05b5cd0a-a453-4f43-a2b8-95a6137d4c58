import * as admin from "firebase-admin";
import { OrderEntity, OrderStatus } from "../types";
import { lockFunds } from "./balance-service";
import { getNextCounterValue } from "./counter-service";
import { validateOrderCreation } from "./order-validation-service";

export interface OrderCreationParams {
  userId: string;
  collectionId: string;
  amount: number;
  owned_gift_id?: string;
  userType: "buyer" | "seller";
}

export interface OrderCreationResult {
  success: boolean;
  orderId: string;
  message: string;
  lockedAmount: number;
  lockPercentage: number;
}

export async function createOrder(
  db: admin.firestore.Firestore,
  params: OrderCreationParams
): Promise<OrderCreationResult> {
  const { userId, collectionId, amount, owned_gift_id, userType } = params;

  // Validate order creation requirements
  const validation = await validateOrderCreation(
    db,
    userId,
    collectionId,
    amount,
    userType
  );

  // Lock funds for the user
  await lockFunds(userId, validation.lockedAmount);

  // Get next order number
  const orderNumber = await getNextCounterValue("order_number");

  // Prepare order data based on user type
  const orderData: Omit<OrderEntity, "id"> = {
    number: orderNumber,
    collectionId,
    amount,
    status: OrderStatus.ACTIVE,
    owned_gift_id,
    createdAt: admin.firestore.FieldValue.serverTimestamp() as any,
    updatedAt: admin.firestore.FieldValue.serverTimestamp() as any,
  };

  // Set buyer or seller ID based on user type
  if (userType === "buyer") {
    orderData.buyerId = userId;
  } else {
    orderData.sellerId = userId;
  }

  // Create the order
  const orderRef = await db.collection("orders").add(orderData);

  return {
    success: true,
    orderId: orderRef.id,
    message: `Order created successfully with ${
      validation.lockedAmount
    } TON locked (${validation.lockPercentage * 100}% of ${amount} TON order)`,
    lockedAmount: validation.lockedAmount,
    lockPercentage: validation.lockPercentage,
  };
}

export async function createBuyerOrder(
  db: admin.firestore.Firestore,
  buyerId: string,
  collectionId: string,
  amount: number,
  owned_gift_id?: string
): Promise<OrderCreationResult> {
  return createOrder(db, {
    userId: buyerId,
    collectionId,
    amount,
    owned_gift_id,
    userType: "buyer",
  });
}

export async function createSellerOrder(
  db: admin.firestore.Firestore,
  sellerId: string,
  collectionId: string,
  amount: number,
  owned_gift_id?: string
): Promise<OrderCreationResult> {
  return createOrder(db, {
    userId: sellerId,
    collectionId,
    amount,
    owned_gift_id,
    userType: "seller",
  });
}
