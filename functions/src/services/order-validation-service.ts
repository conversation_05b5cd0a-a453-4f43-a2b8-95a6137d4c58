import { HttpsError } from "firebase-functions/v2/https";
import * as admin from "firebase-admin";
import { OrderEntity, OrderStatus, Collection } from "../types";
import { hasAvailableBalance } from "./balance-service";
import { getAppConfig } from "./fee-service";
import {
  DEFAULT_BUYER_LOCK_PERCENTAGE,
  DEFAULT_SELLER_LOCK_PERCENTAGE,
} from "../constants";
import { safeMultiply } from "../utils";

export interface OrderValidationResult {
  collection: Collection;
  lockedAmount: number;
  lockPercentage: number;
}

export interface PurchaseValidationResult {
  order: OrderEntity;
  lockedAmount: number;
  lockPercentage: number;
}

export async function validateCollectionAndFloorPrice(
  db: admin.firestore.Firestore,
  collectionId: string,
  amount: number
): Promise<Collection> {
  const collectionDoc = await db
    .collection("collections")
    .doc(collectionId)
    .get();

  if (!collectionDoc.exists) {
    throw new HttpsError("not-found", "Collection not found.");
  }

  const collection = collectionDoc.data() as Collection;

  if (amount < collection?.floorPrice) {
    throw new HttpsError(
      "invalid-argument",
      `Order amount must be at least ${collection?.floorPrice} TON (collection floor price).`
    );
  }

  return collection;
}

export async function validateBalanceAndCalculateLock(
  userId: string,
  amount: number,
  userType: "buyer" | "seller"
): Promise<{ lockedAmount: number; lockPercentage: number }> {
  const config = await getAppConfig();

  const lockPercentage =
    userType === "buyer"
      ? config?.buyer_lock_percentage ?? DEFAULT_BUYER_LOCK_PERCENTAGE
      : config?.seller_lock_percentage ?? DEFAULT_SELLER_LOCK_PERCENTAGE;

  const lockedAmount = safeMultiply(amount, lockPercentage);

  const hasBalance = await hasAvailableBalance(userId, lockedAmount);
  if (!hasBalance) {
    throw new HttpsError(
      "failed-precondition",
      `Insufficient balance. You need at least ${lockedAmount} TON available (${
        lockPercentage * 100
      }% of ${amount} TON order amount).`
    );
  }

  return { lockedAmount, lockPercentage };
}

export async function validateOrderCreation(
  db: admin.firestore.Firestore,
  userId: string,
  collectionId: string,
  amount: number,
  userType: "buyer" | "seller"
): Promise<OrderValidationResult> {
  const collection = await validateCollectionAndFloorPrice(
    db,
    collectionId,
    amount
  );
  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock(userId, amount, userType);

  return { collection, lockedAmount, lockPercentage };
}

export async function getAndValidateOrder(
  db: admin.firestore.Firestore,
  orderId: string
): Promise<OrderEntity> {
  const orderDoc = await db.collection("orders").doc(orderId).get();

  if (!orderDoc.exists) {
    throw new HttpsError("not-found", "Order not found.");
  }

  return { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;
}

export function validateOrderAvailableForPurchase(order: OrderEntity): void {
  if (order.status !== OrderStatus.ACTIVE) {
    throw new HttpsError(
      "failed-precondition",
      "Order is not available for purchase."
    );
  }
}

export function validateBuyerPurchaseConstraints(
  order: OrderEntity,
  buyerId: string
): void {
  // Check if order already has a buyer
  if (order.buyerId && order.buyerId !== buyerId) {
    throw new HttpsError("failed-precondition", "Order already has a buyer.");
  }

  // Check if buyer is trying to buy their own order
  if (order.sellerId === buyerId) {
    throw new HttpsError(
      "failed-precondition",
      "You cannot buy your own order."
    );
  }
}

export function validateSellerPurchaseConstraints(
  order: OrderEntity,
  sellerId: string
): void {
  // Check if order already has a seller
  if (order.sellerId && order.sellerId !== sellerId) {
    throw new HttpsError("failed-precondition", "Order already has a seller.");
  }

  // Check if seller is trying to sell to their own order
  if (order.buyerId === sellerId) {
    throw new HttpsError(
      "failed-precondition",
      "You cannot sell to your own order."
    );
  }
}

export async function validateBuyerPurchase(
  db: admin.firestore.Firestore,
  buyerId: string,
  orderId: string
): Promise<PurchaseValidationResult> {
  const order = await getAndValidateOrder(db, orderId);

  validateOrderAvailableForPurchase(order);
  validateBuyerPurchaseConstraints(order, buyerId);

  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock(buyerId, order.amount, "buyer");

  return { order, lockedAmount, lockPercentage };
}

export async function validateSellerPurchase(
  db: admin.firestore.Firestore,
  sellerId: string,
  orderId: string
): Promise<PurchaseValidationResult> {
  const order = await getAndValidateOrder(db, orderId);

  validateOrderAvailableForPurchase(order);
  validateSellerPurchaseConstraints(order, sellerId);

  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock(sellerId, order.amount, "seller");

  return { order, lockedAmount, lockPercentage };
}
