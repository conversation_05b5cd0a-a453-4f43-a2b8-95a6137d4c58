import * as admin from "firebase-admin";
import { Collection, CollectionStatus } from "../types";

// Deadline constants
export const DEADLINE_DAYS = 7;
export const DEADLINE_MS = DEADLINE_DAYS * 24 * 60 * 60 * 1000;

export const LIMITED_COLLECTION_DEADLINE_DAYS = 30;
export const LIMITED_COLLECTION_DEADLINE_MS =
  LIMITED_COLLECTION_DEADLINE_DAYS * 24 * 60 * 60 * 1000;

export function createDeadline(
  days: number = DEADLINE_DAYS
): admin.firestore.Timestamp {
  const deadlineMs = days * 24 * 60 * 60 * 1000;
  const deadline = new Date(Date.now() + deadlineMs);
  return admin.firestore.Timestamp.fromDate(deadline);
}

export function createStandardDeadline(): admin.firestore.Timestamp {
  return createDeadline(DEADLINE_DAYS);
}

export function createLimitedCollectionDeadline(): admin.firestore.Timestamp {
  return createDeadline(LIMITED_COLLECTION_DEADLINE_DAYS);
}

export async function addDeadlineIfMarketCollection(
  db: admin.firestore.Firestore,
  collectionId: string,
  orderId: string,
  updateData: any
): Promise<void> {
  const collectionDoc = await db
    .collection("collections")
    .doc(collectionId)
    .get();

  if (collectionDoc.exists) {
    const collection = collectionDoc.data() as Collection;
    if (collection.status === CollectionStatus.MARKET) {
      updateData.deadline = createStandardDeadline();
      console.log(
        `Added ${DEADLINE_DAYS}-day deadline to order ${orderId} for MARKET collection ${collectionId}`
      );
    }
  }
}

export function isOrderExpired(deadline: admin.firestore.Timestamp): boolean {
  const now = new Date();
  const deadlineDate = deadline.toDate();
  return now > deadlineDate;
}

export async function getExpiredOrders(
  db: admin.firestore.Firestore
): Promise<admin.firestore.QueryDocumentSnapshot[]> {
  const now = admin.firestore.Timestamp.now();

  const expiredOrdersQuery = await db
    .collection("orders")
    .where("deadline", "<=", now)
    .where("status", "==", "paid")
    .get();

  return expiredOrdersQuery.docs;
}

export async function addDeadlineToOrders(
  db: admin.firestore.Firestore,
  collectionId: string
): Promise<number> {
  const ordersQuery = await db
    .collection("orders")
    .where("collectionId", "==", collectionId)
    .where("status", "==", "paid")
    .get();

  const ordersToUpdate = ordersQuery.docs.filter((doc) => !doc.data().deadline);

  if (ordersToUpdate.length === 0) {
    console.log(
      `No orders need deadline updates for collection ${collectionId}`
    );
    return 0;
  }

  const BATCH_SIZE = 500;
  let totalUpdatedCount = 0;

  for (let i = 0; i < ordersToUpdate.length; i += BATCH_SIZE) {
    const chunk = ordersToUpdate.slice(i, i + BATCH_SIZE);
    const batch = db.batch();

    for (const orderDoc of chunk) {
      batch.update(orderDoc.ref, {
        deadline: createLimitedCollectionDeadline(),
      });
    }

    await batch.commit();
    totalUpdatedCount += chunk.length;

    console.log(
      `Processed batch ${Math.floor(i / BATCH_SIZE) + 1}: Updated ${
        chunk.length
      } orders`
    );
  }

  console.log(
    `Added deadlines to ${totalUpdatedCount} orders for collection ${collectionId}`
  );

  return totalUpdatedCount;
}

export function getTimeUntilDeadline(deadline: admin.firestore.Timestamp): {
  days: number;
  hours: number;
  minutes: number;
  isExpired: boolean;
} {
  const now = new Date();
  const deadlineDate = deadline.toDate();
  const timeDiff = deadlineDate.getTime() - now.getTime();

  if (timeDiff <= 0) {
    return { days: 0, hours: 0, minutes: 0, isExpired: true };
  }

  const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  const hours = Math.floor(
    (timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
  );
  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

  return { days, hours, minutes, isExpired: false };
}
