import { Caption } from '@telegram-apps/telegram-ui';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface DepositDrawerAmountInputProps {
  value: string;
  onChange: (value: string) => void;
  minAmount: number;
  isValid: boolean;
  hasValue: boolean;
}

export function DepositDrawerAmountInput({
  value,
  onChange,
  minAmount,
  isValid,
  hasValue,
}: DepositDrawerAmountInputProps) {
  return (
    <div>
      <Label
        htmlFor="deposit-amount"
        className="text-sm font-medium text-[#f5f5f5]"
      >
        Deposit Amount (TON)
      </Label>
      <Input
        id="deposit-amount"
        type="number"
        placeholder={`Min ${minAmount} TON`}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="mt-2 bg-[#232e3c]/50 border-[#3a4a5c]/50 text-[#f5f5f5] placeholder:text-[#708499] focus:border-[#6ab2f2] focus:ring-[#6ab2f2]/20"
        min={minAmount}
        step="0.1"
      />
      {hasValue && !isValid && (
        <Caption level="2" weight="3" className="text-[#ec3942] mt-1">
          Amount must be at least {minAmount} TON
        </Caption>
      )}
    </div>
  );
}
