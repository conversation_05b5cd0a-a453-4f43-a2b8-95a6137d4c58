import { createServer, IncomingMessage, ServerResponse } from "http";
import { HealthcheckService } from "./healthcheck";
import bot from "../bot";

export class HttpServer {
  private server: ReturnType<typeof createServer> | null = null;
  private readonly port: number;

  constructor(port: number = 8081) {
    this.port = port;
  }

  private async handleHealthcheck(res: ServerResponse): Promise<void> {
    try {
      const lastHealthcheck = await HealthcheckService.getLastHealthcheck();
      const isHealthy = await HealthcheckService.isHealthy();

      const response = {
        status: isHealthy ? "healthy" : "unhealthy",
        lastHealthcheck,
        timestamp: new Date().toISOString(),
        service: "marketplace-bot",
      };

      res.writeHead(isHealthy ? 200 : 503, {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      });
      res.end(JSON.stringify(response, null, 2));
    } catch (error) {
      console.error("Error in healthcheck endpoint:", error);

      const errorResponse = {
        status: "error",
        message: "Failed to check health status",
        timestamp: new Date().toISOString(),
        service: "marketplace-bot",
      };

      res.writeHead(500, {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      });
      res.end(JSON.stringify(errorResponse, null, 2));
    }
  }

  private async handleWebhook(
    req: IncomingMessage,
    res: ServerResponse
  ): Promise<void> {
    try {
      let body = "";

      req.on("data", (chunk) => {
        body += chunk.toString();
      });

      req.on("end", async () => {
        try {
          const update = JSON.parse(body);
          console.log("📨 Received webhook update:", update.update_id);

          // Process the update with Telegraf
          await bot.handleUpdate(update);

          res.writeHead(200, { "Content-Type": "application/json" });
          res.end(JSON.stringify({ ok: true }));
        } catch (error) {
          console.error("Error processing webhook update:", error);
          res.writeHead(500, { "Content-Type": "application/json" });
          res.end(
            JSON.stringify({ ok: false, error: "Failed to process update" })
          );
        }
      });

      req.on("error", (error) => {
        console.error("Error reading webhook request:", error);
        res.writeHead(400, { "Content-Type": "application/json" });
        res.end(JSON.stringify({ ok: false, error: "Bad request" }));
      });
    } catch (error) {
      console.error("Error in webhook handler:", error);
      res.writeHead(500, { "Content-Type": "application/json" });
      res.end(JSON.stringify({ ok: false, error: "Internal server error" }));
    }
  }

  private async handleRequest(
    req: IncomingMessage,
    res: ServerResponse
  ): Promise<void> {
    const url = req.url;
    const method = req.method;

    console.log(`${method} ${url}`);

    if (url === "/healthcheck" && method === "GET") {
      await this.handleHealthcheck(res);
      return;
    }

    if (url === "/webhook" && method === "POST") {
      await this.handleWebhook(req, res);
      return;
    }

    if (url === "/" && method === "GET") {
      const response = {
        service: "marketplace-bot",
        status: "running",
        timestamp: new Date().toISOString(),
        endpoints: ["/healthcheck", "/webhook"],
      };

      res.writeHead(200, {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      });
      res.end(JSON.stringify(response, null, 2));
      return;
    }

    // 404 for unknown routes
    const notFoundResponse = {
      error: "Not Found",
      message: `Route ${url} not found`,
      timestamp: new Date().toISOString(),
    };

    res.writeHead(404, {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
    });
    res.end(JSON.stringify(notFoundResponse, null, 2));
  }

  start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = createServer((req, res) => {
          this.handleRequest(req, res).catch((error) => {
            console.error("Request handling error:", error);
            if (!res.headersSent) {
              res.writeHead(500, { "Content-Type": "application/json" });
              res.end(JSON.stringify({ error: "Internal Server Error" }));
            }
          });
        });

        this.server.listen(this.port, () => {
          console.log(`🌐 HTTP server running on port ${this.port}`);
          console.log(
            `📊 Healthcheck endpoint: http://localhost:${this.port}/healthcheck`
          );
          resolve();
        });

        this.server.on("error", (error) => {
          console.error("HTTP server error:", error);
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          console.log("🛑 HTTP server stopped");
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

const PORT = parseInt(process.env.PORT ?? "8080");
export const httpServer = new HttpServer(PORT);
