'use client';

import { initData, useSignal } from '@telegram-apps/sdk-react';
import { signInWithCustomToken } from 'firebase/auth';
import { httpsCallable } from 'firebase/functions';
import { useState } from 'react';

import { MOCK_USER_ID } from '@/mockEnv';
import { firebaseAuth, firebaseFunctions } from '@/root-context';

interface UseTelegramAuthOptions {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const useTelegramAuth = ({
  onSuccess,
  onError,
}: UseTelegramAuthOptions = {}) => {
  const [isLoading, setIsLoading] = useState(false);

  const initDataUser = useSignal(initData.user);
  const initDataRaw = useSignal(initData.raw);

  const isInTelegram =
    typeof window !== 'undefined' &&
    initDataRaw &&
    initDataUser?.id !== MOCK_USER_ID;

  const authenticate = async () => {
    if (!isInTelegram) {
      onError?.(
        'This app must be opened from Telegram Web App. Please open it from a Telegram bot or mini app.',
      );
      return;
    }

    if (!initDataRaw) {
      onError?.(
        'Telegram Web App is not properly initialized. Please wait a moment and try again.',
      );
      return;
    }

    setIsLoading(true);

    try {
      if (!initDataUser || !initDataRaw) {
        throw new Error(
          "Unable to retrieve Telegram user data. Please ensure you're opening this from a Telegram Web App.",
        );
      }

      let initDataToSend = initDataRaw;

      if (!initDataToSend) {
        const telegramWebApp = window.Telegram?.WebApp;
        if (telegramWebApp?.initData) {
          initDataToSend = telegramWebApp.initData;
        } else if (telegramWebApp?.initDataUnsafe) {
          const unsafeData = telegramWebApp.initDataUnsafe;
          const params = new URLSearchParams();

          if (unsafeData.user) {
            params.set('user', JSON.stringify(unsafeData.user));
          }
          if (unsafeData.chat_instance) {
            params.set('chat_instance', unsafeData.chat_instance);
          }
          if (unsafeData.chat_type) {
            params.set('chat_type', unsafeData.chat_type);
          }
          if (unsafeData.auth_date) {
            params.set('auth_date', unsafeData.auth_date.toString());
          }
          if ('start_param' in unsafeData && unsafeData.start_param) {
            params.set('start_param', String(unsafeData.start_param));
          }

          params.set('hash', 'unsafe_fallback_hash');
          initDataToSend = params.toString();
        }
      }

      const authenticateWithTelegram = httpsCallable(
        firebaseFunctions,
        'authenticateWithTelegram',
      );

      const result = await authenticateWithTelegram({
        initData: initDataToSend,
        useLocalBotToken: process.env.NODE_ENV === 'development',
      });

      const { customToken } = result.data as { customToken: string };
      await signInWithCustomToken(firebaseAuth, customToken);

      onSuccess?.();
    } catch (error) {
      let errorMessage = 'Authentication failed';

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (
        typeof error === 'object' &&
        error !== null &&
        'message' in error
      ) {
        errorMessage = String(error.message);
      }

      if (errorMessage.includes('Failed to get Telegram data')) {
        errorMessage =
          "Unable to access Telegram user data. Please ensure you're opening this app from within Telegram.";
      } else if (errorMessage.includes('initData')) {
        errorMessage =
          'Telegram Web App data is missing. Please try refreshing the app or reopening from Telegram.';
      } else if (
        errorMessage.includes('internal') ||
        errorMessage.includes('INTERNAL')
      ) {
        errorMessage =
          'Server configuration error. The Telegram authentication service needs to be set up. Please contact the administrator.';
      } else if (
        errorMessage.includes('UNAUTHENTICATED') ||
        errorMessage.includes('permission-denied')
      ) {
        errorMessage =
          "Authentication was rejected. Please ensure you're using a valid Telegram account and try again.";
      }

      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    authenticate,
    isLoading,
    isInTelegram,
  };
};
