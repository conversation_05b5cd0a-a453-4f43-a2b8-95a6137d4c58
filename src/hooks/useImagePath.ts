'use client';

import { useEffect, useState } from 'react';

import { getImagePathWithFallback } from '@/utils/image-path';

interface UseImagePathOptions {
  collectionId: string;
  format?: 'png' | 'tgs';
}

interface UseImagePathResult {
  src: string;
  isLoading: boolean;
  isCdn: boolean;
  error: boolean;
  onError: () => void;
}

/**
 * Hook to get image path with CDN fallback
 * Automatically tries CDN first and falls back to local path on error
 */
export const useImagePath = ({
  collectionId,
  format = 'png',
}: UseImagePathOptions): UseImagePathResult => {
  const [src, setSrc] = useState<string>('');
  const [isCdn, setIsCdn] = useState<boolean>(false);
  const [error, setError] = useState<boolean>(false);

  useEffect(() => {
    if (!collectionId) {
      setSrc('');
      setIsCdn(false);
      setError(false);
      return;
    }

    setError(false);

    const { primary } = getImagePathWithFallback(collectionId, format);

    // Start with CDN URL
    setSrc(primary);
    setIsCdn(true);
  }, [collectionId, format]);

  const handleError = () => {
    if (isCdn) {
      // If CDN failed, try local fallback
      const { fallback } = getImagePathWithFallback(collectionId, format);
      setSrc(fallback);
      setIsCdn(false);
      setError(false);
    } else {
      // If local also failed, mark as error
      setError(true);
    }
  };

  return {
    src,
    isLoading: false, // Let the Image component handle loading state
    isCdn,
    error,
    onError: handleError,
  };
};
