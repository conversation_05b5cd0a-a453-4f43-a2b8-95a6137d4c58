import bot from "./bot";
import { redisService } from "./services/redis";
import { HealthcheckService } from "./services/healthcheck";
import { httpServer } from "./services/http-server";
import { loadEnvironment } from "./config/env-loader";

loadEnvironment();

const PORT = process.env.PORT ?? 3001;
const NODE_ENV = process.env.NODE_ENV ?? "development";
const WEBHOOK_URL = process.env.WEBHOOK_URL;

// Validate environment variables for production
if (NODE_ENV === "production") {
  if (!WEBHOOK_URL) {
    console.error("❌ WEBHOOK_URL is required for production deployment");
    process.exit(1);
  }

  if (!WEBHOOK_URL.startsWith("https://")) {
    console.error("❌ WEBHOOK_URL must use HTTPS in production");
    process.exit(1);
  }

  console.log("✅ Production environment variables validated");
}

async function setupWebhook() {
  try {
    const webhookUrl = `${WEBHOOK_URL}/webhook`;

    // Delete any existing webhook first
    await bot.telegram.deleteWebhook({ drop_pending_updates: true });
    console.log("🗑️ Cleared existing webhook and dropped pending updates");

    // Wait a moment for the deletion to take effect
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Set the new webhook with configuration
    await bot.telegram.setWebhook(webhookUrl, {
      max_connections: 40,
      allowed_updates: [
        "message",
        "callback_query",
        "inline_query",
        "chosen_inline_result",
        "edited_message",
        "channel_post",
        "edited_channel_post",
      ],
    });
    console.log(`✅ Webhook set to: ${webhookUrl}`);

    // Verify webhook is set correctly
    const webhookInfo = await bot.telegram.getWebhookInfo();
    console.log("📡 Webhook info:", {
      url: webhookInfo.url,
      has_custom_certificate: webhookInfo.has_custom_certificate,
      pending_update_count: webhookInfo.pending_update_count,
      last_error_date: webhookInfo.last_error_date,
      last_error_message: webhookInfo.last_error_message,
      max_connections: webhookInfo.max_connections,
      allowed_updates: webhookInfo.allowed_updates,
    });

    if (webhookInfo.url !== webhookUrl) {
      throw new Error(
        `Webhook URL mismatch. Expected: ${webhookUrl}, Got: ${webhookInfo.url}`
      );
    }

    if (
      webhookInfo.pending_update_count &&
      webhookInfo.pending_update_count > 0
    ) {
      console.log(
        `⚠️ Warning: ${webhookInfo.pending_update_count} pending updates found`
      );
    }
  } catch (error) {
    console.error("❌ Failed to set webhook:", error);
    throw error;
  }
}

const WEB_APP_URL = process.env.WEB_APP_URL as string;

async function startBot() {
  try {
    console.log("🤖 Starting Marketplace Bot...");

    // Initialize Redis connection
    console.log("🔗 Connecting to Redis...");
    await redisService.connect();

    // Write healthcheck to Redis
    await HealthcheckService.writeInitHealthcheck();

    // Start HTTP server for health checks
    console.log("🌐 Starting HTTP server...");
    await httpServer.start();

    const botInfo = await bot.telegram.getMe();
    console.log(`✅ Bot @${botInfo.username} is ready`);
    console.log(`🔍 Bot ID: ${botInfo.id}`);

    if (NODE_ENV === "production" && WEBHOOK_URL) {
      console.log("🌐 Setting up webhook for production...");
      await setupWebhook();
      console.log(`🚀 Bot webhook server started on port ${PORT}`);
    } else {
      console.log("🔄 Starting bot in polling mode (development)...");

      console.log("🔄 Launching bot...");
      bot
        .launch()
        .then(() => {
          console.log("🚀 Bot started successfully in polling mode");
        })
        .catch((error: any) => {
          console.error("❌ Failed to launch bot:", error);
          if (
            error.message?.includes("409") ||
            error.message?.includes("Conflict")
          ) {
            console.log(
              "💡 Another bot instance might be running. Please stop it first."
            );
          }
        });
    }

    console.log("🔧 Setting up bot configuration...");

    try {
      await bot.telegram.setChatMenuButton({
        menuButton: {
          type: "web_app",
          text: "PREM",
          web_app: {
            url: WEB_APP_URL,
          },
        },
      });

      console.log("✅ Menu button configured");
    } catch (error) {
      console.log("⚠️ Failed to set menu button:", error);
    }

    try {
      await bot.telegram.setMyCommands([
        { command: "start", description: "Start the bot and show main menu" },
        { command: "help", description: "Show help information" },
        { command: "health", description: "Check bot health status" },
      ]);
      console.log("✅ Commands configured");
    } catch (error) {
      console.log("⚠️ Failed to set commands:", error);
    }

    try {
      await bot.telegram.setMyDescription(
        "🛍️ Marketplace Bot - Your gateway to the PREM marketplace platform. Use the menu button or commands to get started!"
      );
      await bot.telegram.setMyShortDescription(
        "🛍️ Access the marketplace platform"
      );
      console.log("✅ Description configured");
    } catch (error) {
      console.log("⚠️ Failed to set description:", error);
    }

    console.log("🎉 Bot setup completed successfully!");
    console.log("📋 Available buttons:");
    console.log("  🛒 My Buy Orders");
    console.log("  💰 My Sell Orders");
    console.log("  🔗 Get Referral Link");
    console.log("  📞 Contact Support");
    console.log("  🌐 Open Marketplace (Web App)");
  } catch (error) {
    console.error("❌ Failed to start bot:", error);
    process.exit(1);
  }
}

async function gracefulShutdown(signal: string) {
  console.log(`🛑 Received ${signal}, shutting down gracefully...`);

  try {
    // Stop the bot
    bot.stop(signal);

    // If we're in production with webhook, clean it up
    if (NODE_ENV === "production" && WEBHOOK_URL) {
      console.log("🗑️ Cleaning up webhook...");
      try {
        await bot.telegram.deleteWebhook();
        console.log("✅ Webhook cleaned up");
      } catch (error) {
        console.error("⚠️ Failed to clean up webhook:", error);
      }
    }

    // Stop HTTP server
    await httpServer.stop();

    // Disconnect from Redis
    await redisService.disconnect();

    console.log("✅ Graceful shutdown completed");
    process.exit(0);
  } catch (error) {
    console.error("❌ Error during shutdown:", error);
    process.exit(1);
  }
}

process.once("SIGINT", () => gracefulShutdown("SIGINT"));
process.once("SIGTERM", () => gracefulShutdown("SIGTERM"));

startBot();
