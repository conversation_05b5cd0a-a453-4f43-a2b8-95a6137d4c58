import { globalCache } from '@/utils/cache-utils';

interface ImagePathOptions {
  collectionId: string;
  format?: 'png' | 'tgs';
}

interface ImagePathResult {
  src: string;
  isCdn: boolean;
}

const CDN_BASE_URL = 'https://cdn.changes.tg/gifts/originals';
const CACHE_CONFIG = { duration: 5 * 60 * 1000 }; // 5 minutes

const checkCdnAvailability = async (url: string): Promise<boolean> => {
  const cached = globalCache.getCdnAvailability(url);
  if (cached !== null) {
    return cached;
  }

  try {
    const response = await fetch(url, { method: 'HEAD' });
    const available = response.ok;

    globalCache.setCdnAvailability(url, available, CACHE_CONFIG);

    return available;
  } catch (error) {
    console.warn(`CDN availability check failed for ${url}:`, error);
    globalCache.setCdnAvailability(url, false, CACHE_CONFIG);
    return false;
  }
};

/**
 * Get image path, trying CDN first and falling back to local path
 */
export const getImagePath = async ({
  collectionId,
  format = 'png',
}: ImagePathOptions): Promise<ImagePathResult> => {
  if (!collectionId) {
    return {
      src: '',
      isCdn: false,
    };
  }

  const cdnUrl = `${CDN_BASE_URL}/${collectionId}`;
  const localPath = `/limited/${collectionId}/Original.${format}`;

  // Try CDN first
  const isCdnAvailable = await checkCdnAvailability(cdnUrl);

  if (isCdnAvailable) {
    return {
      src: cdnUrl,
      isCdn: true,
    };
  }

  // Fall back to local path
  return {
    src: localPath,
    isCdn: false,
  };
};

/**
 * Synchronous version that returns CDN URL first, with fallback handling in component
 */
export const getImageSrc = (collectionId: string): string => {
  if (!collectionId) return '';
  return `/limited/${collectionId}/Original.png`;
};

/**
 * Synchronous version for TGS files
 */
export const getTgsUrl = (collectionId: string): string => {
  if (!collectionId) return '';
  return `/limited/${collectionId}/Original.tgs`;
};

/**
 * Get CDN URL for images
 */
export const getCdnImageUrl = (collectionId: string): string => {
  if (!collectionId) return '';
  return `${CDN_BASE_URL}/${collectionId}`;
};

/**
 * Clear the CDN availability cache
 */
export const clearImagePathCache = (): void => {
  globalCache.invalidatePattern('cdn:');
};

/**
 * Enhanced image path function with CDN fallback
 * Returns CDN URL first, then local path as fallback
 */
export const getImagePathWithFallback = (
  collectionId: string,
  format: 'png' | 'tgs' = 'png',
) => {
  if (!collectionId) return { primary: '', fallback: '' };

  const cdnUrl = `${CDN_BASE_URL}/${collectionId}/Original.${format}`;
  const localPath = `/limited/${collectionId}/Original.${format}`;

  return {
    primary: cdnUrl,
    fallback: localPath,
  };
};
