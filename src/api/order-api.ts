import type { DocumentSnapshot } from 'firebase/firestore';
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getCountFromServer,
  getDocs,
  limit,
  orderBy,
  query,
  startAfter,
  updateDoc,
  where,
  writeBatch,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import type { OrderEntity } from '@/core.constants';
import { firebaseFunctions, firestore } from '@/root-context';

const COLLECTION_NAME = 'orders';

export interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy?: 'price_asc' | 'price_desc' | 'date_asc' | 'date_desc';
  limit?: number;
  lastDoc?: DocumentSnapshot | null; // DocumentSnapshot for pagination
  currentUserId?: string; // Filter out orders belonging to current user
}

export interface PaginatedOrdersResult {
  orders: OrderEntity[];
  lastDoc: DocumentSnapshot | null; // DocumentSnapshot for next page
  hasMore: boolean;
}

export const createOrder = async (
  orderData: Omit<OrderEntity, 'id' | 'createdAt' | 'updatedAt'>,
) => {
  try {
    // Filter out undefined values to avoid Firestore errors
    const cleanOrderData = Object.fromEntries(
      Object.entries(orderData).filter(([, value]) => value !== undefined),
    );

    const docRef = await addDoc(collection(firestore, COLLECTION_NAME), {
      ...cleanOrderData,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Cache will be cleared by components using the cache context

    return docRef.id;
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
};

export const updateOrder = async (
  id: string,
  orderData: Partial<OrderEntity>,
) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await updateDoc(docRef, {
      ...orderData,
      updatedAt: new Date(),
    });

    // Cache will be cleared by components using the cache context
  } catch (error) {
    console.error('Error updating order:', error);
    throw error;
  }
};

export const deleteOrder = async (id: string) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await deleteDoc(docRef);

    // Cache will be cleared by components using the cache context
  } catch (error) {
    console.error('Error deleting order:', error);
    throw error;
  }
};

export const getOrders = async (
  pageSize: number = 10,
  lastDoc?: DocumentSnapshot,
) => {
  try {
    let q = query(
      collection(firestore, COLLECTION_NAME),
      orderBy('createdAt', 'desc'),
      limit(pageSize),
    );

    if (lastDoc) {
      q = query(
        collection(firestore, COLLECTION_NAME),
        orderBy('createdAt', 'desc'),
        startAfter(lastDoc),
        limit(pageSize),
      );
    }

    const snapshot = await getDocs(q);
    const orders: (OrderEntity & { id: string })[] = [];

    snapshot.forEach((doc) => {
      orders.push({ id: doc.id, ...doc.data() } as OrderEntity & {
        id: string;
      });
    });

    return {
      orders,
      lastDoc: snapshot.docs[snapshot.docs.length - 1],
      hasMore: snapshot.docs.length === pageSize,
    };
  } catch (error) {
    console.error('Error fetching orders:', error);
    throw error;
  }
};

export const clearAllOrders = async () => {
  try {
    const snapshot = await getDocs(collection(firestore, COLLECTION_NAME));
    const batch = writeBatch(firestore);

    snapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log(`Deleted ${snapshot.docs.length} orders`);

    // Cache will be cleared by components using the cache context
  } catch (error) {
    console.error('Error clearing orders:', error);
    throw error;
  }
};

export const createBulkOrders = async (
  orders: Omit<OrderEntity, 'id' | 'createdAt' | 'updatedAt'>[],
) => {
  try {
    const batch = writeBatch(firestore);
    const orderCollection = collection(firestore, COLLECTION_NAME);

    orders.forEach((orderData) => {
      const docRef = doc(orderCollection);

      // Filter out undefined values to avoid Firestore errors
      const cleanOrderData = Object.fromEntries(
        Object.entries(orderData).filter(([, value]) => value !== undefined),
      );

      batch.set(docRef, {
        ...cleanOrderData,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    });

    await batch.commit();
    console.log(`Created ${orders.length} orders`);

    // Cache will be cleared by components using the cache context
  } catch (error) {
    console.error('Error creating bulk orders:', error);
    throw error;
  }
};

export const getPaidOrdersCount = async (): Promise<number> => {
  try {
    const q = query(
      collection(firestore, COLLECTION_NAME),
      where('status', '==', 'paid'),
    );

    const snapshot = await getCountFromServer(q);
    return snapshot.data().count;
  } catch (error) {
    console.error('Error getting paid orders count:', error);
    throw error;
  }
};

export interface OrderStats {
  totalOrders: number;
  paidOrdersWithoutSecondaryPrice: number;
  paidOrdersWithSecondaryPrice: number;
  giftSentToRelayerOrders: number;
  fulfilledOrders: number;
  cancelledOrders: number;
}

export const getOrderStats = async (): Promise<OrderStats> => {
  try {
    const ordersCollection = collection(firestore, COLLECTION_NAME);

    // Execute all count queries in parallel for better performance
    const [
      totalOrdersSnapshot,
      giftSentToRelayerSnapshot,
      fulfilledOrdersSnapshot,
      cancelledOrdersSnapshot,
    ] = await Promise.all([
      getCountFromServer(ordersCollection),
      getCountFromServer(
        query(ordersCollection, where('status', '==', 'gift_sent_to_relayer')),
      ),
      getCountFromServer(
        query(ordersCollection, where('status', '==', 'fulfilled')),
      ),
      getCountFromServer(
        query(ordersCollection, where('status', '==', 'cancelled')),
      ),
    ]);

    // Get paid orders to separate those with and without secondary market price
    const paidOrdersQuery = query(
      ordersCollection,
      where('status', '==', 'paid'),
    );
    const paidOrdersDocs = await getDocs(paidOrdersQuery);

    let paidOrdersWithoutSecondaryPrice = 0;
    let paidOrdersWithSecondaryPrice = 0;

    paidOrdersDocs.docs.forEach((doc) => {
      const orderData = doc.data();
      if (
        orderData.secondaryMarketPrice !== null &&
        orderData.secondaryMarketPrice !== undefined
      ) {
        paidOrdersWithSecondaryPrice++;
      } else {
        paidOrdersWithoutSecondaryPrice++;
      }
    });

    const stats: OrderStats = {
      totalOrders: totalOrdersSnapshot.data().count,
      paidOrdersWithoutSecondaryPrice,
      paidOrdersWithSecondaryPrice,
      giftSentToRelayerOrders: giftSentToRelayerSnapshot.data().count,
      fulfilledOrders: fulfilledOrdersSnapshot.data().count,
      cancelledOrders: cancelledOrdersSnapshot.data().count,
    };

    return stats;
  } catch (error) {
    console.error('Error getting order statistics:', error);
    throw error;
  }
};

// getOrdersForSellers moved to seller-orders-api.ts

// getOrdersForBuyers moved to buyer-orders-api.ts

// getSecondaryMarketOrders moved to secondary-market-orders-api.ts (to be created)

// Get orders for current user (where user is buyer or seller)
export const getUserOrders = async (userId: string): Promise<OrderEntity[]> => {
  try {
    console.log('🔍 getUserOrders called for userId:', userId);

    // Get orders where user is buyer
    const buyerOrdersQuery = query(
      collection(firestore, COLLECTION_NAME),
      where('buyerId', '==', userId),
    );

    // Get orders where user is seller
    const sellerOrdersQuery = query(
      collection(firestore, COLLECTION_NAME),
      where('sellerId', '==', userId),
    );

    const [buyerSnapshot, sellerSnapshot] = await Promise.all([
      getDocs(buyerOrdersQuery),
      getDocs(sellerOrdersQuery),
    ]);

    const orders: OrderEntity[] = [];

    // Add buyer orders
    buyerSnapshot.forEach((doc) => {
      orders.push({ id: doc.id, ...doc.data() } as OrderEntity);
    });

    // Add seller orders
    sellerSnapshot.forEach((doc) => {
      orders.push({ id: doc.id, ...doc.data() } as OrderEntity);
    });

    // Remove duplicates (in case user is both buyer and seller of same order)
    const uniqueOrders = orders.filter(
      (order, index, self) =>
        index === self.findIndex((o) => o.id === order.id),
    );

    // Sort orders by priority: gift_sent_to_relayer, paid, active, cancelled, fulfilled
    const statusPriority = {
      gift_sent_to_relayer: 1,
      paid: 2,
      active: 3,
      cancelled: 4,
      fulfilled: 5,
    };

    uniqueOrders.sort((a, b) => {
      const aPriority =
        statusPriority[a.status as keyof typeof statusPriority] || 999;
      const bPriority =
        statusPriority[b.status as keyof typeof statusPriority] || 999;

      if (aPriority !== bPriority) {
        return aPriority - bPriority;
      }

      // If same priority, sort by creation date (newest first)
      const aDate = a.createdAt ? new Date(a.createdAt).getTime() : 0;
      const bDate = b.createdAt ? new Date(b.createdAt).getTime() : 0;
      return bDate - aDate;
    });

    console.log(`📊 Found ${uniqueOrders.length} orders for user ${userId}`);
    return uniqueOrders;
  } catch (error) {
    console.error('Error fetching user orders:', error);
    throw error;
  }
};

// Debug function to see all orders in the database
export const debugAllOrders = async (): Promise<void> => {
  try {
    console.log('🔍 DEBUG: Fetching all orders from database...');
    const q = query(collection(firestore, COLLECTION_NAME));
    const snapshot = await getDocs(q);

    console.log(`📊 Total orders in database: ${snapshot.docs.length}`);

    snapshot.docs.forEach((doc, index) => {
      const orderData = { id: doc.id, ...doc.data() } as OrderEntity;
      console.log(`📋 Order ${index + 1}:`, {
        id: orderData.id,
        buyerId: orderData.buyerId,
        sellerId: orderData.sellerId,
        status: orderData.status,
        amount: orderData.amount,
        collectionId: orderData.collectionId,
        createdAt: orderData.createdAt,
      });
    });
  } catch (error) {
    console.error('Error fetching all orders:', error);
  }
};

// Secondary Market Functions

export interface SetSecondaryMarketPriceResponse {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
}

export interface CancelOrderResponse {
  success: boolean;
  message: string;
  order: {
    id: string;
    number: number;
    status: string;
  };
}

export const setSecondaryMarketPrice = async (
  orderId: string,
  secondaryMarketPrice: number,
): Promise<SetSecondaryMarketPriceResponse> => {
  try {
    const setSecondaryMarketPriceFunction = httpsCallable<
      { orderId: string; secondaryMarketPrice: number },
      SetSecondaryMarketPriceResponse
    >(firebaseFunctions, 'setSecondaryMarketPrice');

    const result = await setSecondaryMarketPriceFunction({
      orderId,
      secondaryMarketPrice,
    });

    // Cache will be cleared by components using the cache context

    return result.data;
  } catch (error) {
    console.error('Error setting secondary market price:', error);
    throw error;
  }
};

export const cancelOrder = async (
  orderId: string,
  userId: string,
): Promise<CancelOrderResponse> => {
  try {
    const cancelOrderFunction = httpsCallable<
      { orderId: string; userId: string },
      CancelOrderResponse
    >(firebaseFunctions, 'cancelOrder');

    const result = await cancelOrderFunction({
      orderId,
      userId,
    });

    // Cache will be cleared by components using the cache context

    return result.data;
  } catch (error) {
    console.error('Error cancelling order:', error);
    throw error;
  }
};

// clearOrdersCache functionality moved to individual API files
