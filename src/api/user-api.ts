import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import type { UserBalance, UserEntity } from '@/core.constants';
import { firebaseFunctions, firestore } from '@/root-context';

import { getCurrentUserId } from './auth-api';

export interface UpdateUserData {
  id?: string;
  name?: string;
  displayName?: string;
  email?: string;
  photoURL?: string;
  tg_id?: string;
  ton_wallet_address?: string;
  raw_ton_wallet_address?: string;
  referral_id?: string;
  referral_fee?: number;
  balance?: UserBalance;
  role?: 'admin' | 'user';
}

interface CloudFunctionResponse {
  success: boolean;
  message: string;
  updatedFields: string[];
}

export const updateUser = async (
  userId: string,
  updateData: UpdateUserData,
): Promise<UserEntity> => {
  try {
    const currentUserId = getCurrentUserId();

    if (!currentUserId) {
      throw new Error('User not authenticated with Firebase Auth');
    }

    const changeUserData = httpsCallable(firebaseFunctions, 'changeUserData');

    const cloudFunctionData: any = {
      userId: userId,
    };

    if (updateData.displayName !== undefined) {
      cloudFunctionData.name = updateData.displayName;
    }
    if (updateData.tg_id !== undefined) {
      cloudFunctionData.tg_id = updateData.tg_id;
    }
    if (updateData.ton_wallet_address !== undefined) {
      cloudFunctionData.ton_wallet_address = updateData.ton_wallet_address;
    }
    if (updateData.raw_ton_wallet_address !== undefined) {
      cloudFunctionData.raw_ton_wallet_address =
        updateData.raw_ton_wallet_address;
    }
    if (updateData.referral_id !== undefined) {
      cloudFunctionData.referral_id = updateData.referral_id;
    }

    const result = await changeUserData(cloudFunctionData);
    const response = result.data as CloudFunctionResponse;

    if (!response.success) {
      throw new Error(response.message || 'Failed to update user');
    }

    const updatedUser = await getUserById(userId);
    if (!updatedUser) {
      throw new Error(`User with ID ${userId} not found after update`);
    }

    return updatedUser;
  } catch (error) {
    console.error(`Error updating user ${userId}:`, error);
    throw error;
  }
};

export const getUserById = async (
  userId: string,
): Promise<UserEntity | null> => {
  try {
    const userRef = doc(firestore, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return null;
    }

    return {
      id: userDoc.id,
      ...userDoc.data(),
    } as UserEntity;
  } catch (error) {
    console.error(`Error getting user ${userId}:`, error);
    throw error;
  }
};

export const updateUserWallet = async (
  userId: string,
  tonWalletAddress: string,
  rawTonWalletAddress?: string,
): Promise<UserEntity> => {
  try {
    const updateData: UpdateUserData = {
      ton_wallet_address: tonWalletAddress,
    };

    if (rawTonWalletAddress !== undefined) {
      updateData.raw_ton_wallet_address = rawTonWalletAddress;
    }

    return await updateUser(userId, updateData);
  } catch (error) {
    console.error(`Error updating wallet for user ${userId}:`, error);
    throw error;
  }
};

export const getUsersWithCustomReferralFees = async () => {
  try {
    const usersRef = collection(firestore, 'users');
    const q = query(usersRef, where('referral_fee', '!=', null));
    const querySnapshot = await getDocs(q);

    const users: UserEntity[] = [];
    querySnapshot.forEach((doc) => {
      const userData = doc.data() as UserEntity;
      if (userData.referral_fee !== undefined && userData.referral_fee > 0) {
        users.push({ ...userData, id: doc.id });
      }
    });

    return users;
  } catch (error) {
    console.error('Error getting users with custom referral fees:', error);
    throw error;
  }
};
