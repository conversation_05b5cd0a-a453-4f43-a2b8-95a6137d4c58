import { doc, getDoc, setDoc } from 'firebase/firestore';

import { firestore } from '@/root-context';
import { globalCache } from '@/utils/cache-utils';

import type { AppConfig } from './app-config-api';

const APP_CONFIG_COLLECTION = 'app_config';
const APP_CONFIG_DOC_ID = 'fees';

const CACHE_CONFIG = { duration: 5 * 60 * 1000 }; // 5 minutes

export const loadFeesConfig = async (): Promise<AppConfig | null> => {
  const cachedConfig = globalCache.getFeesConfig<AppConfig>();
  if (cachedConfig) {
    return cachedConfig;
  }

  try {
    const configDoc = await getDoc(
      doc(firestore, APP_CONFIG_COLLECTION, APP_CONFIG_DOC_ID),
    );

    if (configDoc.exists()) {
      const config = configDoc.data() as AppConfig;
      globalCache.setFeesConfig(config, CACHE_CONFIG);
      return config;
    }

    return null;
  } catch (error) {
    console.error('Error loading fees config:', error);
    throw error;
  }
};

export const updateFeesConfig = async (config: AppConfig): Promise<void> => {
  try {
    await setDoc(
      doc(firestore, APP_CONFIG_COLLECTION, APP_CONFIG_DOC_ID),
      config,
    );

    globalCache.invalidate('app_config:fees');
  } catch (error) {
    console.error('Error updating fees config:', error);
    throw error;
  }
};

export const clearFeesCache = () => {
  globalCache.invalidate('app_config:fees');
};
