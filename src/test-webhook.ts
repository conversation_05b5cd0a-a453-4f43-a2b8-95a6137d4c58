import { Telegraf } from "telegraf";
import { loadEnvironment } from "./config/env-loader";

loadEnvironment();

const BOT_TOKEN = process.env.BOT_TOKEN;
const WEBHOOK_URL = process.env.WEBHOOK_URL;

if (!BOT_TOKEN) {
  console.error("❌ BOT_TOKEN is required");
  process.exit(1);
}

if (!WEBHOOK_URL) {
  console.error("❌ WEBHOOK_URL is required");
  process.exit(1);
}

async function testWebhook() {
  if (!BOT_TOKEN) {
    console.error("❌ BOT_TOKEN is required");
    process.exit(1);
  }

  const bot = new Telegraf(BOT_TOKEN);

  try {
    console.log("🔍 Testing webhook configuration...");

    // Get current webhook info
    const webhookInfo = await bot.telegram.getWebhookInfo();
    console.log("📡 Current webhook info:", {
      url: webhookInfo.url,
      has_custom_certificate: webhookInfo.has_custom_certificate,
      pending_update_count: webhookInfo.pending_update_count,
      last_error_date: webhookInfo.last_error_date,
      last_error_message: webhookInfo.last_error_message,
      max_connections: webhookInfo.max_connections,
      allowed_updates: webhookInfo.allowed_updates,
    });

    // Test if webhook URL is accessible
    const webhookUrl = `${WEBHOOK_URL}/webhook`;
    console.log(`🌐 Testing webhook URL: ${webhookUrl}`);

    if (webhookInfo.url === webhookUrl) {
      console.log("✅ Webhook is correctly configured");
    } else if (webhookInfo.url) {
      console.log(
        `⚠️ Webhook URL mismatch. Current: ${webhookInfo.url}, Expected: ${webhookUrl}`
      );
    } else {
      console.log("❌ No webhook is currently set");
    }

    // Check for errors
    if (webhookInfo.last_error_date) {
      const errorDate = new Date(webhookInfo.last_error_date * 1000);
      console.log(
        `⚠️ Last webhook error: ${
          webhookInfo.last_error_message
        } at ${errorDate.toISOString()}`
      );
    } else {
      console.log("✅ No webhook errors detected");
    }

    // Check pending updates
    if (
      webhookInfo.pending_update_count &&
      webhookInfo.pending_update_count > 0
    ) {
      console.log(
        `⚠️ ${webhookInfo.pending_update_count} pending updates found`
      );
    } else {
      console.log("✅ No pending updates");
    }
  } catch (error) {
    console.error("❌ Error testing webhook:", error);
  }
}

testWebhook();
