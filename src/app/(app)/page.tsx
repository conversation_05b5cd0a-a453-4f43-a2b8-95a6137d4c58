'use client';

import { useEffect, useState } from 'react';

import { Tabs, TabsContent } from '@/components/ui/tabs';
import { useAppCache } from '@/contexts/AppCacheContext';
import type { OrderEntity } from '@/core.constants';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { useRootContext } from '@/root-context';

import { MarketplaceFilters } from './marketplace/components/marketplace-filters';
import { MarketplaceHeader } from './marketplace/components/marketplace-header';
import { MarketplaceOrderList } from './marketplace/components/marketplace-order-list';
import { MarketplaceTabs } from './marketplace/components/marketplace-tabs';
import { CreateOrderDrawer } from './marketplace/create-order-drawer';
import { useMarketplaceFilters } from './marketplace/hooks/use-marketplace-filters';
import type { TabType } from './marketplace/hooks/use-marketplace-orders';
import { useMarketplaceOrders } from './marketplace/hooks/use-marketplace-orders';
import { OrderDetailsDrawer } from './marketplace/order-details-drawer';

export default function MarketplacePage() {
  const { collections, refetchUser, currentUser } = useRootContext();
  const cache = useAppCache();
  const [activeTab, setActiveTab] = useState<TabType>('sellers');
  const [showCreateOrderDrawer, setShowCreateOrderDrawer] = useState(false);
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);

  const filters = useMarketplaceFilters();
  const ordersFilters = {
    ...filters.getFilters(),
    currentUserId: currentUser?.id,
  };

  const { sellersState, buyersState, loadOrders, loadMoreOrders } =
    useMarketplaceOrders({
      activeTab,
      filters: ordersFilters,
    });

  const sellersLoadMoreRef = useInfiniteScroll({
    hasMore: sellersState.hasMore,
    loading: sellersState.loading || sellersState.loadingMore,
    onLoadMore: () => {
      if (activeTab === 'sellers') {
        loadMoreOrders();
      }
    },
  });

  const buyersLoadMoreRef = useInfiniteScroll({
    hasMore: buyersState.hasMore,
    loading: buyersState.loading || buyersState.loadingMore,
    onLoadMore: () => {
      if (activeTab === 'buyers') {
        loadMoreOrders();
      }
    },
  });

  useEffect(() => {
    loadOrders(true);
  }, [loadOrders]);

  const handleCreateOrder = () => {
    setShowCreateOrderDrawer(true);
  };

  const handleOrderCreated = () => {
    cache.invalidatePattern('getOrdersForBuyers');
    cache.invalidatePattern('getOrdersForSellers');
    cache.invalidatePattern('getSecondaryMarketOrders');
    loadOrders(true);
    refetchUser();
  };

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderAction = handleOrderCreated;

  return (
    <div className="space-y-2 bg-[#17212b] min-h-screen">
      <MarketplaceHeader onCreateOrder={handleCreateOrder} />

      <Tabs value={activeTab}>
        <MarketplaceTabs activeTab={activeTab} onTabChange={setActiveTab} />

        <MarketplaceFilters
          minPrice={filters.minPrice}
          maxPrice={filters.maxPrice}
          selectedCollection={filters.selectedCollection}
          sortBy={filters.sortBy}
          collections={collections}
          onMinPriceChange={filters.setMinPrice}
          onMaxPriceChange={filters.setMaxPrice}
          onCollectionChange={filters.setSelectedCollection}
          onSortChange={filters.setSortBy}
        />

        <TabsContent value="sellers" className="space-y-4">
          <MarketplaceOrderList
            orders={sellersState.orders}
            collections={collections}
            loading={sellersState.loading}
            loadingMore={sellersState.loadingMore}
            emptyMessage="No orders found for sellers"
            onOrderClick={handleOrderClick}
            ref={sellersLoadMoreRef}
          />
        </TabsContent>

        <TabsContent value="buyers" className="space-y-4">
          <MarketplaceOrderList
            orders={buyersState.orders}
            collections={collections}
            loading={buyersState.loading}
            loadingMore={buyersState.loadingMore}
            emptyMessage="No orders found for buyers"
            onOrderClick={handleOrderClick}
            ref={buyersLoadMoreRef}
          />
        </TabsContent>
      </Tabs>

      <CreateOrderDrawer
        open={showCreateOrderDrawer}
        onOpenChange={setShowCreateOrderDrawer}
        orderType={activeTab === 'sellers' ? 'seller' : 'buyer'}
        onOrderCreated={handleOrderCreated}
      />

      <OrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        orderType={activeTab === 'sellers' ? 'seller' : 'buyer'}
        onOrderAction={handleOrderAction}
      />
    </div>
  );
}
