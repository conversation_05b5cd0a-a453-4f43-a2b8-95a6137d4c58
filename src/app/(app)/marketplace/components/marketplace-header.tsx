'use client';

import { Button as TgButton } from '@telegram-apps/telegram-ui';
import { Plus } from 'lucide-react';

interface MarketplaceHeaderProps {
  onCreateOrder: () => void;
}

export const MarketplaceHeader = ({
  onCreateOrder,
}: MarketplaceHeaderProps) => {
  return (
    <div className="flex items-center justify-between">
      <h1 className="text-2xl font-bold text-[#f5f5f5]">Marketplace</h1>
      <TgButton
        onClick={onCreateOrder}
        className="[&>h6]:flex [&>h6]:items-center [&>h6]:gap-1"
      >
        <Plus className="w-4 h-4 stroke-[2.5]" />
        <span>Create</span>
      </TgButton>
    </div>
  );
};
