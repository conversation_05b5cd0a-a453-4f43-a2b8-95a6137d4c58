import { useState } from 'react';

import type { SortType } from './use-marketplace-orders';

interface UseMarketplaceFiltersReturn {
  minPrice: string;
  maxPrice: string;
  selectedCollection: string;
  sortBy: SortType;
  setMinPrice: (value: string) => void;
  setMaxPrice: (value: string) => void;
  setSelectedCollection: (value: string) => void;
  setSortBy: (value: SortType) => void;
  getFilters: () => {
    minPrice?: number;
    maxPrice?: number;
    collectionId?: string;
    sortBy: SortType;
  };
  resetFilters: () => void;
}

const validatePriceInput = (value: string): boolean => {
  return value === '' || /^\d*\.?\d*$/.test(value);
};

export const useMarketplaceFilters = (): UseMarketplaceFiltersReturn => {
  const [minPrice, setMinPriceState] = useState('');
  const [maxPrice, setMaxPriceState] = useState('');
  const [selectedCollection, setSelectedCollection] = useState<string>('all');
  const [sortBy, setSortBy] = useState<SortType>('date_desc');

  const setMinPrice = (value: string) => {
    if (validatePriceInput(value)) {
      setMinPriceState(value);
    }
  };

  const setMaxPrice = (value: string) => {
    if (validatePriceInput(value)) {
      setMaxPriceState(value);
    }
  };

  const getFilters = () => ({
    minPrice: minPrice ? parseFloat(minPrice) : undefined,
    maxPrice: maxPrice ? parseFloat(maxPrice) : undefined,
    collectionId: selectedCollection !== 'all' ? selectedCollection : undefined,
    sortBy,
  });

  const resetFilters = () => {
    setMinPriceState('');
    setMaxPriceState('');
    setSelectedCollection('all');
    setSortBy('date_desc');
  };

  return {
    minPrice,
    maxPrice,
    selectedCollection,
    sortBy,
    setMinPrice,
    setMaxPrice,
    setSelectedCollection,
    setSortBy,
    getFilters,
    resetFilters,
  };
};
