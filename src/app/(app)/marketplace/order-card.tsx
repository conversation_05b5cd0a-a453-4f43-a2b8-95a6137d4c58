'use client';

import { Button, Caption } from '@telegram-apps/telegram-ui';

import { TgsOrImage } from '@/components/TgsOrImage';
import { TonLogo } from '@/components/TonLogo';
import { Card, CardContent } from '@/components/ui/card';
import type { Collection, OrderEntity } from '@/core.constants';

interface OrderCardProps {
  order: OrderEntity;
  collection: Collection | undefined;
  onClick: () => void;
  animated?: boolean;
}

export function OrderCard({
  animated,
  order,
  collection,
  onClick,
}: OrderCardProps) {
  return (
    <Card
      className="bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441] transition-colors cursor-pointer group"
      onClick={onClick}
    >
      <CardContent className="p-2 flex flex-col">
        <div className="aspect-square relative rounded-lg overflow-hidden bg-[#17212b]">
          <TgsOrImage
            isImage={!animated}
            collectionId={order.collectionId}
            imageProps={{
              alt: collection?.name || 'Order item',
              fill: true,
              className:
                'object-cover group-hover:scale-105 transition-transform duration-200',
            }}
            tgsProps={{
              style: { height: 'auto', width: 'auto', padding: '16px' },
            }}
          />
        </div>

        <div className="flex items-center justify-between mt-2 mb-2">
          <Caption level="1" weight="1" className="truncate">
            {collection?.name || 'Unknown Collection'}
          </Caption>
          <Caption level="2" weight="3" className="w-fit text-[#78797e]">
            #
            {order.number ||
              (typeof order.id === 'string' ? order.id?.slice(-6) : 'N/A')}
          </Caption>
        </div>

        <Button className="w-full [&>h6]:flex [&>h6]:items-center [&>h6]:justify-center [&>h6]:gap-1">
          {order.amount} <TonLogo size={16} className="w-7 h-7 -ml-2" />
        </Button>
      </CardContent>
    </Card>
  );
}
