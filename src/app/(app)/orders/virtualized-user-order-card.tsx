'use client';

import React from 'react';

import { GridItem } from '@/components/ui/virtualized-grid';
import type { OrderEntity } from '@/core.constants';

import { UserOrderCard } from './user-order-card';

interface VirtualizedUserOrderCardProps {
  order: OrderEntity;
  userRole: 'seller' | 'buyer';
  onClick: () => void;
  index: number;
  initialRenderedCount?: number;
}

export const VirtualizedUserOrderCard: React.FC<
  VirtualizedUserOrderCardProps
> = ({ order, userRole, onClick, index, initialRenderedCount = 8 }) => {
  const itemId = `user-order-${order.id}`;

  return (
    <GridItem
      itemId={itemId}
      index={index}
      initialRenderedCount={initialRenderedCount}
    >
      <UserOrderCard order={order} userRole={userRole} onClick={onClick} />
    </GridItem>
  );
};
