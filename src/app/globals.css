@import 'tailwindcss';
@import 'tw-animate-css';
@import '@telegram-apps/telegram-ui/dist/styles.css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  /* Custom breakpoints */
  --breakpoint-xss: 375px;
  --breakpoint-xs: 475px;

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* TON Brand Colors */
  --color-ton-main: #0098ea;
  --color-ton-gray: #f7f9fb;
  --color-ton-black: #1e2337;

  /* Telegram Brand Colors */
  --color-telegram-blue: #0088cc;
  --color-telegram-light-blue: #6ab2f2;
  --color-telegram-dark-bg: #17212b;
  --color-telegram-secondary-bg: #232e3c;
  --color-telegram-text: #f5f5f5;
  --color-telegram-hint: #708499;
}

:root {
  --radius: 0.625rem;

  /* Telegram Light Theme Colors */
  --background: #ffffff;
  --foreground: #000000;
  --card: #ffffff;
  --card-foreground: #000000;
  --popover: #ffffff;
  --popover-foreground: #000000;
  --primary: #007aff;
  --primary-foreground: #ffffff;
  --secondary: #f2f2f7;
  --secondary-foreground: #000000;
  --muted: #f2f2f7;
  --muted-foreground: #8e8e93;
  --accent: #007aff;
  --accent-foreground: #ffffff;
  --destructive: #ff3b30;
  --border: #c6c6c8;
  --input: #f2f2f7;
  --ring: #007aff;
  --chart-1: #007aff;
  --chart-2: #34c759;
  --chart-3: #ff9500;
  --chart-4: #ff3b30;
  --chart-5: #af52de;
  --sidebar: #f2f2f7;
  --sidebar-foreground: #000000;
  --sidebar-primary: #007aff;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f2f2f7;
  --sidebar-accent-foreground: #000000;
  --sidebar-border: #c6c6c8;
  --sidebar-ring: #007aff;

  /* Telegram Theme Variables (Light) */
  --tg-theme-bg-color: #ffffff;
  --tg-theme-text-color: #000000;
  --tg-theme-hint-color: #8e8e93;
  --tg-theme-link-color: #007aff;
  --tg-theme-button-color: #007aff;
  --tg-theme-button-text-color: #ffffff;
  --tg-theme-secondary-bg-color: #f2f2f7;
  --tg-theme-header-bg-color: #ffffff;
  --tg-theme-bottom-bar-bg-color: #ffffff;
  --tg-theme-accent-text-color: #007aff;
  --tg-theme-section-bg-color: #ffffff;
  --tg-theme-section-header-text-color: #007aff;
  --tg-theme-section-separator-color: #c6c6c8;
  --tg-theme-subtitle-text-color: #8e8e93;
  --tg-theme-destructive-text-color: #ff3b30;
}

.dark {
  /* Telegram Dark Theme Colors */
  --background: #17212b;
  --foreground: #f5f5f5;
  --card: #232e3c;
  --card-foreground: #f5f5f5;
  --popover: #232e3c;
  --popover-foreground: #f5f5f5;
  --primary: #6ab2f2;
  --primary-foreground: #ffffff;
  --secondary: #232e3c;
  --secondary-foreground: #f5f5f5;
  --muted: #232e3c;
  --muted-foreground: #708499;
  --accent: #6ab2f2;
  --accent-foreground: #ffffff;
  --destructive: #ec3942;
  --border: #3a4a5c;
  --input: #2a3441;
  --ring: #6ab2f2;
  --chart-1: #6ab2f2;
  --chart-2: #5288c1;
  --chart-3: #708499;
  --chart-4: #ec3942;
  --chart-5: #6ab3f3;
  --sidebar: #232e3c;
  --sidebar-foreground: #f5f5f5;
  --sidebar-primary: #6ab2f2;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #232e3c;
  --sidebar-accent-foreground: #f5f5f5;
  --sidebar-border: #3a4a5c;
  --sidebar-ring: #6ab2f2;

  /* Telegram Theme Variables (Dark) */
  --tg-theme-bg-color: #17212b;
  --tg-theme-text-color: #f5f5f5;
  --tg-theme-hint-color: #708499;
  --tg-theme-link-color: #6ab3f3;
  --tg-theme-button-color: #5288c1;
  --tg-theme-button-text-color: #ffffff;
  --tg-theme-secondary-bg-color: #232e3c;
  --tg-theme-header-bg-color: #17212b;
  --tg-theme-bottom-bar-bg-color: #17212b;
  --tg-theme-accent-text-color: #6ab2f2;
  --tg-theme-section-bg-color: #17212b;
  --tg-theme-section-header-text-color: #6ab3f3;
  --tg-theme-section-separator-color: #3a4a5c;
  --tg-theme-subtitle-text-color: #708499;
  --tg-theme-destructive-text-color: #ec3942;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Remove margin-right when scroll is locked */
  body[data-scroll-locked] {
    margin-right: 0 !important;
  }

  /* Custom scrollbar styles - Telegram themed */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: var(--tg-theme-hint-color)
      var(--tg-theme-secondary-bg-color);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: var(--tg-theme-secondary-bg-color);
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: var(--tg-theme-hint-color);
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: var(--tg-theme-accent-text-color);
  }

  /* Force scrollbar visibility for collection select */
  [data-radix-popper-content-wrapper] {
    z-index: 9999 !important;
  }

  /* Ensure custom dropdown is scrollable and contained */
  .collection-select-dropdown {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  .collection-select-dropdown::-webkit-scrollbar {
    width: 6px;
  }

  .collection-select-dropdown::-webkit-scrollbar-track {
    background: transparent;
  }

  .collection-select-dropdown::-webkit-scrollbar-thumb {
    background: var(--tg-theme-hint-color);
    border-radius: 3px;
  }

  .collection-select-dropdown::-webkit-scrollbar-thumb:hover {
    background: var(--tg-theme-accent-text-color);
  }

  /* Ensure popover works properly in drawer context */
  .drawer-content [data-radix-popper-content-wrapper] {
    max-height: 45vh !important;
    overflow: visible !important;
    z-index: 9999 !important;
  }

  .drawer-content [data-slot='popover-content'] {
    max-height: 40vh !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* Collection select dropdown scrolling */
  .collection-select-dropdown {
    scroll-behavior: smooth;
    overscroll-behavior-y: contain;
    overflow-y: scroll !important;
    flex: 1 1 auto;
  }

  /* Force scrollbar visibility */
  .collection-select-dropdown::-webkit-scrollbar {
    width: 8px !important;
    background: transparent;
  }

  .collection-select-dropdown::-webkit-scrollbar-thumb {
    background: var(--tg-theme-hint-color) !important;
    border-radius: 4px;
  }

  .collection-select-dropdown::-webkit-scrollbar-thumb:hover {
    background: var(--tg-theme-accent-text-color) !important;
  }

  /* Ensure drawer context doesn't interfere */
  .drawer-content .collection-select-dropdown {
    position: relative !important;
    overflow-y: scroll !important;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  /* Collection select dialog positioning */
  .collection-select-dialog {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 95vw !important;
    max-width: 32rem !important;
    max-height: 85vh !important;
    margin: 0 !important;
    border-radius: 1rem !important;
  }

  /* Mobile adjustments */
  @media (max-width: 640px) {
    .collection-select-dialog {
      width: 98vw !important;
      max-height: 90vh !important;
    }
  }
}

/* Mobile drawer fixes */
@media (max-width: 768px) {
  /* Ensure drawers work properly on mobile */
  [data-vaul-drawer] {
    touch-action: none;
  }

  [data-vaul-drawer][data-vaul-drawer-direction='bottom'] {
    transform: translate3d(0, 100%, 0);
  }

  [data-vaul-drawer][data-vaul-drawer-direction='bottom'][data-state='open'] {
    transform: translate3d(0, 0, 0);
  }

  /* Fix for mobile viewport height issues */
  [data-vaul-drawer-wrapper] {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
  }

  /* Prevent body scroll when drawer is open */
  body:has([data-vaul-drawer][data-state='open']) {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }
}
