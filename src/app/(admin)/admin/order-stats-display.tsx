'use client';

import type { LucideIcon } from 'lucide-react';
import {
  Bar<PERSON>hart3,
  CheckCircle,
  DollarSign,
  Gift,
  RefreshCw,
  ShoppingCart,
  XCircle,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import type { OrderStats } from '@/api/order-api';
import { getOrderStats } from '@/api/order-api';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

export const OrderStatsDisplay = () => {
  const { toast } = useToast();
  const [stats, setStats] = useState<OrderStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const loadOrderStats = useCallback(async () => {
    try {
      setIsLoading(true);
      const orderStats = await getOrderStats();
      setStats(orderStats);
    } catch (error) {
      console.error('Error loading order statistics:', error);
      toast({
        title: 'Error',
        description: 'Failed to load order statistics',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      await loadOrderStats();
      toast({
        title: 'Success',
        description: 'Order statistics refreshed successfully',
      });
    } catch (error) {
      console.error('Error refreshing order statistics:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    loadOrderStats();
  }, [loadOrderStats]);

  const StatCard = ({
    title,
    value,
    icon: Icon,
    description,
  }: {
    title: string;
    value: number;
    icon: LucideIcon;
    description: string;
  }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {isLoading ? 'Loading...' : value.toLocaleString()}
        </div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2 text-lg">
                <BarChart3 className="h-4 w-4" />
                Order Statistics
              </CardTitle>
              <CardDescription className="text-sm">
                Real-time order statistics across all statuses
              </CardDescription>
            </div>
            <Button
              onClick={handleRefresh}
              disabled={isRefreshing}
              size="sm"
              variant="outline"
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`}
              />
              Refresh
            </Button>
          </div>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <StatCard
          title="Total Orders"
          value={stats?.totalOrders ?? 0}
          icon={ShoppingCart}
          description="All orders in the system"
        />

        <StatCard
          title="Paid Orders (No Secondary)"
          value={stats?.paidOrdersWithoutSecondaryPrice ?? 0}
          icon={DollarSign}
          description="Paid orders without secondary market price"
        />

        <StatCard
          title="Paid Orders (With Secondary)"
          value={stats?.paidOrdersWithSecondaryPrice ?? 0}
          icon={DollarSign}
          description="Paid orders with secondary market price set"
        />

        <StatCard
          title="Gift Sent to Relayer"
          value={stats?.giftSentToRelayerOrders ?? 0}
          icon={Gift}
          description="Orders where gift was sent to relayer"
        />

        <StatCard
          title="Fulfilled Orders"
          value={stats?.fulfilledOrders ?? 0}
          icon={CheckCircle}
          description="Successfully completed orders"
        />

        <StatCard
          title="Cancelled Orders"
          value={stats?.cancelledOrders ?? 0}
          icon={XCircle}
          description="Orders that were cancelled"
        />
      </div>

      {stats && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Order Status Breakdown</CardTitle>
            <CardDescription>
              Distribution of orders across different statuses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Total Paid Orders:</span>
                <span className="font-semibold">
                  {(
                    stats.paidOrdersWithoutSecondaryPrice +
                    stats.paidOrdersWithSecondaryPrice
                  ).toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Active Orders:</span>
                <span className="font-semibold">
                  {(
                    stats.totalOrders -
                    stats.paidOrdersWithoutSecondaryPrice -
                    stats.paidOrdersWithSecondaryPrice -
                    stats.giftSentToRelayerOrders -
                    stats.fulfilledOrders -
                    stats.cancelledOrders
                  ).toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Completion Rate:</span>
                <span className="font-semibold">
                  {stats.totalOrders > 0
                    ? `${(
                        (stats.fulfilledOrders / stats.totalOrders) *
                        100
                      ).toFixed(1)}%`
                    : '0%'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
