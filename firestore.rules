rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    function isAdmin() {
      let userDoc = /databases/$(database)/documents/users/$(request.auth.uid);
      return request.auth != null && exists(userDoc) && get(userDoc).data.role == 'admin';
    }

    match /users/{userId} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }

    match /collections/{document=**} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }
    
    match /app_config/{document=**} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }
    
    match /orders/{document=**} {
      allow read: if true; // Everyone can view
      allow create, update, delete: if isAdmin();
    }
  }
}